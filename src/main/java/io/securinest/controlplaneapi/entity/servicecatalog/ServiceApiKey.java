package io.securinest.controlplaneapi.entity.servicecatalog;

import io.securinest.controlplaneapi.entity.shared.AbstractTenantEntity;
import jakarta.persistence.CollectionTable;
import jakarta.persistence.Column;
import jakarta.persistence.ElementCollection;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.Instant;
import java.util.LinkedHashSet;
import java.util.Set;
import java.util.UUID;

@Entity
@Table(name = "service_api_key")
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class ServiceApiKey extends AbstractTenantEntity {

    @NotNull
    @Column(name = "service_id", nullable = false)
    private UUID serviceId;

    @NotBlank
    @Size(max = 120)
    @Column(name = "name", nullable = false, length = 120)
    private String name;

    @NotBlank
    @Pattern(regexp = "^sk_[a-z0-9]{6,12}$")
    @Column(name = "prefix", nullable = false, length = 20, unique = true)
    private String prefix;

    @NotNull
    @Size(max = 255)
    @Column(name = "secret_hash", nullable = false, length = 255)
    private String secretHash;

    @NotNull
    @ElementCollection(fetch = FetchType.LAZY)
    @CollectionTable(name = "service_api_key_scopes", joinColumns = @JoinColumn(name = "service_api_key_id"))
    @Column(name = "scope", nullable = false)
    private Set<String> scopes = new LinkedHashSet<>();

    @Column(name = "last_used_at")
    private Instant lastUsedAt;

    @Column(name = "expires_at")
    private Instant expiresAt;

    @Column(name = "revoked_at")
    private Instant revokedAt;

}
