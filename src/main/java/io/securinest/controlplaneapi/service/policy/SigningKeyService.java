package io.securinest.controlplaneapi.service.policy;

import io.securinest.controlplaneapi.dto.policy.SigningKeyResponse;
import io.securinest.controlplaneapi.entity.policy.SigningKey;
import io.securinest.controlplaneapi.mapper.policy.SigningKeyMapper;
import io.securinest.controlplaneapi.repository.policy.SigningKeyRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class SigningKeyService {

    private final SigningKeyRepository signingKeyRepository;
    private final SigningKeyMapper signingKeyMapper;

    @Transactional(readOnly = true)
    public List<SigningKeyResponse> listPublicKeys(UUID tenantId) {
        log.debug("Listing public signing keys for tenant: {}", tenantId);
        
        List<SigningKey> keys = signingKeyRepository.findAllByTenantIdAndRevokedAtIsNull(tenantId);
        
        return keys.stream()
                .map(signingKeyMapper::toResponse)
                .toList();
    }
    
    @Transactional(readOnly = true)
    public Optional<SigningKeyResponse> getByKid(UUID tenantId, String kid) {
        log.debug("Getting signing key by kid: {} for tenant: {}", kid, tenantId);
        
        return signingKeyRepository.findByTenantIdAndKid(tenantId, kid)
                .map(signingKeyMapper::toResponse);
    }
    
    @Transactional(readOnly = true)
    public Optional<SigningKey> getActiveSigningKey(UUID tenantId) {
        log.debug("Getting active signing key for tenant: {}", tenantId);
        
        // For now, return the first non-revoked key
        // In production, you'd have logic to determine the "active" key
        List<SigningKey> keys = signingKeyRepository.findAllByTenantIdAndRevokedAtIsNull(tenantId);
        
        return keys.isEmpty() ? Optional.empty() : Optional.of(keys.getFirst());
    }
}
