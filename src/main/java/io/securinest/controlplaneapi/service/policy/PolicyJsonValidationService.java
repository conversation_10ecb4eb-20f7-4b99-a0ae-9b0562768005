package io.securinest.controlplaneapi.service.policy;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.networknt.schema.JsonSchema;
import com.networknt.schema.JsonSchemaFactory;
import com.networknt.schema.SpecVersion;
import com.networknt.schema.ValidationMessage;
import io.securinest.controlplaneapi.exceptions.policy.PolicyValidationException;
import io.securinest.controlplaneapi.util.shared.Constants;
import jakarta.annotation.PostConstruct;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class PolicyJsonValidationService {

    private final ObjectMapper objectMapper;
    private JsonSchema policySchema;

    @PostConstruct
    public void init() {
        try {
            // Load the policy JSON schema from classpath
            ClassPathResource schemaResource = new ClassPathResource("schemas/policy.json");
            if (schemaResource.exists()) {
                try (InputStream schemaStream = schemaResource.getInputStream()) {
                    JsonSchemaFactory factory = JsonSchemaFactory.getInstance(SpecVersion.VersionFlag.V7);
                    policySchema = factory.getSchema(schemaStream);
                    log.info("Loaded policy JSON schema successfully");
                }
            } else {
                log.warn("Policy JSON schema not found at schemas/policy.json - validation will be skipped");
            }
        } catch (IOException e) {
            log.error("Failed to load policy JSON schema", e);
            throw new RuntimeException("Failed to initialize policy JSON validation", e);
        }
    }

    public void validatePolicyJson(String policyJson) {
        // Validate size first
        validateJsonSize(policyJson);
        
        // Validate against schema if available
        if (policySchema != null) {
            validateJsonSchema(policyJson);
        } else {
            log.debug("Skipping JSON schema validation - schema not loaded");
        }
    }
    
    private void validateJsonSize(String json) {
        try {
            byte[] jsonBytes = json.getBytes(StandardCharsets.UTF_8);
            
            if (jsonBytes.length > Constants.MAX_JSON_SIZE_BYTES) {
                throw new PolicyValidationException("Policy JSON exceeds maximum size of " + 
                                                 Constants.MAX_JSON_SIZE_BYTES + " bytes (actual: " + jsonBytes.length + " bytes)");
            }
        } catch (Exception e) {
            throw new PolicyValidationException("Failed to validate JSON size: " + e.getMessage());
        }
    }
    
    private void validateJsonSchema(String json) {
        try {
            JsonNode jsonNode = objectMapper.valueToTree(json);
            Set<ValidationMessage> errors = policySchema.validate(jsonNode);
            
            if (!errors.isEmpty()) {
                StringBuilder errorMessage = new StringBuilder("Policy JSON validation failed:");
                for (ValidationMessage error : errors) {
                    errorMessage.append("\n- ").append(error.getMessage());
                }
                throw new PolicyValidationException(errorMessage.toString());
            }
        } catch (Exception e) {
            if (e instanceof PolicyValidationException) {
                throw e;
            }
            throw new PolicyValidationException("Failed to validate JSON schema: " + e.getMessage());
        }
    }
}
