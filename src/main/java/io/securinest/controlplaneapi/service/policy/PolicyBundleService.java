package io.securinest.controlplaneapi.service.policy;

import io.securinest.controlplaneapi.dto.policy.PolicyBundleCompileRequest;
import io.securinest.controlplaneapi.dto.policy.PolicyBundleResponse;
import io.securinest.controlplaneapi.entity.policy.CompliancePolicyVersion;
import io.securinest.controlplaneapi.entity.policy.PolicyBundle;
import io.securinest.controlplaneapi.entity.shared.EnforcementMode;
import io.securinest.controlplaneapi.entity.shared.PolicyState;
import io.securinest.controlplaneapi.exceptions.policy.PolicyNotFoundException;
import io.securinest.controlplaneapi.exceptions.policy.PolicyValidationException;
import io.securinest.controlplaneapi.mapper.policy.PolicyBundleMapper;
import io.securinest.controlplaneapi.repository.policy.CompliancePolicyVersionRepository;
import io.securinest.controlplaneapi.repository.policy.PolicyBundleRepository;
import io.securinest.controlplaneapi.repository.servicecatalog.AppServiceRepository;
import io.securinest.controlplaneapi.repository.servicecatalog.EnvironmentRepository;
import io.securinest.controlplaneapi.repository.servicecatalog.ServiceEnvironmentRepository;
import io.securinest.controlplaneapi.util.shared.Constants;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class PolicyBundleService {

    private final PolicyBundleRepository bundleRepository;
    private final CompliancePolicyVersionRepository versionRepository;
    private final AppServiceRepository serviceRepository;
    private final EnvironmentRepository environmentRepository;
    private final ServiceEnvironmentRepository serviceEnvironmentRepository;
    private final PolicyBundleMapper bundleMapper;
    private final PolicySigningService signingService;


    @Transactional
    public PolicyBundleResponse compileAndSign(UUID tenantId, PolicyBundleCompileRequest request, UUID requesterId) {
        log.debug("Compiling and signing bundle for tenant: {}, service: {}, env: {}", 
                 tenantId, request.serviceId(), request.envId());
        
        // Load and validate policy version
        CompliancePolicyVersion policyVersion = versionRepository.findById(UUID.fromString(request.policyVersionId()))
                .orElseThrow(() -> new PolicyNotFoundException("Policy version not found: " + request.policyVersionId()));

        if (policyVersion.getStatus() != PolicyState.ACTIVE) {
            throw new PolicyValidationException("Policy version must be ACTIVE to compile bundle");
        }

        // Validate service and environment belong to tenant and are linked
        UUID serviceId = UUID.fromString(request.serviceId());
        UUID envId = UUID.fromString(request.envId());

        if (!serviceRepository.existsByIdAndTenantId(serviceId, tenantId)) {
            throw new PolicyNotFoundException("Service not found: " + serviceId);
        }

        if (!environmentRepository.existsByIdAndTenantId(envId, tenantId)) {
            throw new PolicyNotFoundException("Environment not found: " + envId);
        }
        
        // Validate service-environment link
        if (!serviceEnvironmentRepository.existsByServiceIdAndEnvId(serviceId, envId)) {
            throw new PolicyValidationException("Service and environment are not linked");
        }
        
        // Compile authored JSON to normalized compiled JSON
        Map<String, Object> compiledJson = compilePolicy(policyVersion.getJson());
        
        // Validate compiled size
        validateCompiledSize(compiledJson);
        
        // Compute content hash
        String contentHash = computeContentHash(compiledJson);
        String etag = "W/\"" + contentHash + "\"";
        
        // Sign content hash
        PolicySigningService.SigningResult signingResult = signingService.signContentHash(tenantId, contentHash);
        
        // Deactivate any previous active bundle for this scope
        bundleRepository.deactivatePrevious(tenantId, serviceId, envId);
        
        // Create new bundle
        PolicyBundle bundle = new PolicyBundle();
        bundle.setTenantId(tenantId);
        bundle.setPolicyVersionId(policyVersion.getId());
        bundle.setServiceId(serviceId);
        bundle.setEnvId(envId);
        bundle.setCompiledJson(compiledJson);
        bundle.setETag(etag);
        bundle.setSignatureB64(signingResult.signatureB64());
        bundle.setKid(signingResult.kid());
        bundle.setActive(true);
        bundle.setEnforcementMode(EnforcementMode.valueOf(request.enforcementMode()));
        bundle.setLkgTtlSeconds(request.lkgTtlSeconds());
        bundle.setSampling(request.sampling());
        
        PolicyBundle saved = bundleRepository.save(bundle);
        log.info("Created policy bundle: {} for tenant: {}, service: {}, env: {} by user: {}",
                saved.getId(), tenantId, serviceId, envId, requesterId);
        
        return bundleMapper.toResponse(saved);
    }

    @Transactional(readOnly = true)
    public Page<PolicyBundleResponse> list(UUID tenantId, UUID serviceId, UUID envId, Pageable pageable) {
        log.debug("Listing policy bundles for tenant: {}, service: {}, env: {}", 
                 tenantId, serviceId, envId);
        
        Page<PolicyBundle> bundles = bundleRepository
                .findAllByTenantIdAndServiceIdAndEnvId(tenantId, serviceId, envId, pageable);
        
        return bundles.map(bundleMapper::toResponse);
    }

    @Transactional
    public void deactivate(UUID tenantId, UUID serviceId, UUID envId, UUID bundleId) {
        log.debug("Deactivating bundle: {} for tenant: {}, service: {}, env: {}", 
                 bundleId, tenantId, serviceId, envId);
        
        int updated = bundleRepository.deactivateBundle(tenantId, bundleId);
        if (updated == 0) {
            throw new PolicyNotFoundException("Bundle not found or already inactive: " + bundleId);
        }
        
        log.info("Deactivated policy bundle: {} for tenant: {}", bundleId, tenantId);
    }
    
    private Map<String, Object> compilePolicy(String authoredJson) {
        // TODO: Implement policy compilation logic
        // - Normalize rule shapes
        // - Fill default values
        // - Generate deterministic rule IDs
        // - Sort arrays where order doesn't matter
        // - Validate cross-field invariants
        
        // For now, return as-is (placeholder)
        return new HashMap<>() {{ put("compiled", "json"); }};
    }
    
    private void validateCompiledSize(Map<String, Object> compiledJson) {
        // Simple size estimation - in production, serialize to JSON and check byte size
        String jsonString = compiledJson.toString();
        if (jsonString.length() > Constants.MAX_COMPILED_SIZE_BYTES) {
            throw new PolicyValidationException("Compiled policy exceeds maximum size of " +
                Constants.MAX_COMPILED_SIZE_BYTES + " bytes");
        }
    }
    
    private String computeContentHash(Map<String, Object> compiledJson) {
        try {
            // TODO: Use proper JSON canonicalization
            String jsonString = compiledJson.toString();
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(jsonString.getBytes(StandardCharsets.UTF_8));
            return Base64.getUrlEncoder().withoutPadding().encodeToString(hash);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-256 algorithm not available", e);
        }
    }
}
