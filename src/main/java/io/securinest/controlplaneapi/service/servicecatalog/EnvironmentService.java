package io.securinest.controlplaneapi.service.servicecatalog;

import io.securinest.controlplaneapi.dto.servicecatalog.EnvironmentCreateRequest;
import io.securinest.controlplaneapi.dto.servicecatalog.EnvironmentResponse;
import io.securinest.controlplaneapi.dto.servicecatalog.EnvironmentUpdateRequest;
import io.securinest.controlplaneapi.entity.servicecatalog.Environment;
import io.securinest.controlplaneapi.exceptions.shared.SecurinestException;
import io.securinest.controlplaneapi.mapper.servicecatalog.EnvironmentMapper;
import io.securinest.controlplaneapi.repository.servicecatalog.EnvironmentRepository;
import io.securinest.controlplaneapi.util.shared.ValidationUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.orm.ObjectOptimisticLockingFailureException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class EnvironmentService {

    private final EnvironmentRepository environmentRepository;
    private final EnvironmentMapper environmentMapper;

    @Transactional(readOnly = true)
    public List<EnvironmentResponse> findAllEnvironmentsByTenantId(UUID tenantId) {
        return environmentRepository.findAllByTenantId(tenantId)
                .stream()
                .map(environmentMapper::mapToEnvironmentResponse)
                .toList();
    }

    @Transactional
    public EnvironmentResponse create(UUID tenantId, EnvironmentCreateRequest request, UUID requesterId, String requestId) {
        if (tenantId == null) throw new SecurinestException(HttpStatus.BAD_REQUEST, "Missing tenant ID");
        if (request == null) throw new SecurinestException(HttpStatus.BAD_REQUEST, "Missing body");
        if (requesterId == null) throw new SecurinestException(HttpStatus.UNAUTHORIZED, "Missing current user");

        log.debug("Creating environment for tenant: {}", tenantId);

        // Validate and normalize inputs
        String normalizedKey = ValidationUtils.trimToNull(request.key());
        String normalizedName = ValidationUtils.trimToNull(request.name());

        if (normalizedKey == null) throw new SecurinestException(HttpStatus.BAD_REQUEST, "Environment key is required");
        if (normalizedName == null) throw new SecurinestException(HttpStatus.BAD_REQUEST, "Environment name is required");

        // Check for conflicts
        if (environmentRepository.existsByTenantIdAndKey(tenantId, normalizedKey)) {
            throw new SecurinestException(HttpStatus.CONFLICT, "Environment key already exists");
        }

        // Create entity
        Environment environment = environmentMapper.fromRequest(request);
        environment.setTenantId(tenantId);
        environment.setKey(normalizedKey);
        environment.setName(normalizedName);

        try {
            Environment saved = environmentRepository.save(environment);
            log.info("Created environment: {} for tenant: {} by user: {}", saved.getId(), tenantId, requesterId);
            return environmentMapper.mapToEnvironmentResponse(saved);
        } catch (DataIntegrityViolationException e) {
            log.warn("createEnvironment constraint issue. requestId={} msg={}", requestId, e.getMessage());
            throw new SecurinestException(HttpStatus.CONFLICT, "Conflict creating environment");
        }
    }

    @Transactional(readOnly = true)
    public EnvironmentResponse get(UUID tenantId, UUID environmentId) {
        if (tenantId == null) throw new SecurinestException(HttpStatus.BAD_REQUEST, "Missing tenant ID");
        if (environmentId == null) throw new SecurinestException(HttpStatus.BAD_REQUEST, "Missing environment ID");

        Environment environment = environmentRepository.findByIdAndTenantId(environmentId, tenantId)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Environment not found"));

        return environmentMapper.mapToEnvironmentResponse(environment);
    }

    @Transactional
    public EnvironmentResponse update(UUID tenantId, UUID environmentId, EnvironmentUpdateRequest request, Integer expectedVersion, UUID requesterId, String requestId) {
        if (tenantId == null) throw new SecurinestException(HttpStatus.BAD_REQUEST, "Missing tenant ID");
        if (environmentId == null) throw new SecurinestException(HttpStatus.BAD_REQUEST, "Missing environment ID");
        if (request == null) throw new SecurinestException(HttpStatus.BAD_REQUEST, "Missing body");
        if (requesterId == null) throw new SecurinestException(HttpStatus.UNAUTHORIZED, "Missing current user");

        Environment environment = environmentRepository.findByIdAndTenantId(environmentId, tenantId)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Environment not found"));

        if (expectedVersion != null && !expectedVersion.equals((int) environment.getVersion())) {
            throw new SecurinestException(HttpStatus.PRECONDITION_FAILED, "Version mismatch");
        }

        // Apply updates
        boolean hasChanges = false;

        String normalizedName = ValidationUtils.trimToNull(request.name());
        if (normalizedName != null && !normalizedName.equals(environment.getName())) {
            environment.setName(normalizedName);
            hasChanges = true;
        }

        if (!hasChanges) {
            return environmentMapper.mapToEnvironmentResponse(environment);
        }

        try {
            Environment saved = environmentRepository.save(environment);
            log.info("Updated environment: {} for tenant: {} by user: {}", environmentId, tenantId, requesterId);
            return environmentMapper.mapToEnvironmentResponse(saved);
        } catch (ObjectOptimisticLockingFailureException e) {
            throw new SecurinestException(HttpStatus.CONFLICT, "Version mismatch");
        } catch (DataIntegrityViolationException e) {
            log.warn("updateEnvironment constraint issue. requestId={} msg={}", requestId, e.getMessage());
            throw new SecurinestException(HttpStatus.CONFLICT, "Conflict updating environment");
        }
    }

    @Transactional
    public void delete(UUID tenantId, UUID environmentId) {
        if (tenantId == null) throw new SecurinestException(HttpStatus.BAD_REQUEST, "Missing tenant ID");
        if (environmentId == null) throw new SecurinestException(HttpStatus.BAD_REQUEST, "Missing environment ID");

        Environment environment = environmentRepository.findByIdAndTenantId(environmentId, tenantId)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Environment not found"));

        environmentRepository.delete(environment);
        log.info("Deleted environment: {} for tenant: {}", environmentId, tenantId);
    }
}
