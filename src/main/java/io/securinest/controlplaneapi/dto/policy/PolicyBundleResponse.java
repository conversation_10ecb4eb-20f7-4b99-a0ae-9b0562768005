package io.securinest.controlplaneapi.dto.policy;

import lombok.Builder;

import java.time.Instant;

@Builder
public record PolicyBundleResponse(
        String id,
        String tenantId,
        String policyVersionId,
        String serviceId,
        String envId,
        String compiledJson,
        String eTag,
        String signatureB64,
        String kid,
        Boolean active,
        String enforcementMode,
        Integer lkgTtlSeconds,
        String sampling,
        Instant createdAt,
        Instant updatedAt,
        Long version
) {
}
