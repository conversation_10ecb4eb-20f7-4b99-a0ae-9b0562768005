package io.securinest.controlplaneapi.mapper.servicecatalog;

import io.securinest.controlplaneapi.dto.servicecatalog.EnvironmentCreateRequest;
import io.securinest.controlplaneapi.dto.servicecatalog.EnvironmentResponse;
import io.securinest.controlplaneapi.entity.servicecatalog.Environment;
import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(
        componentModel = "spring",
        injectionStrategy = InjectionStrategy.CONSTRUCTOR,
        unmappedTargetPolicy = ReportingPolicy.IGNORE
)
public interface EnvironmentMapper {

    @Mapping(target = "id", expression = "java(environment.getId() != null ? environment.getId().toString() : null)")
    @Mapping(target = "tenantId", expression = "java(environment.getTenantId() != null ? environment.getTenantId().toString() : null)")
    EnvironmentResponse mapToEnvironmentResponse(Environment environment);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "tenantId", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "version", ignore = true)
    Environment fromRequest(EnvironmentCreateRequest request);
}
