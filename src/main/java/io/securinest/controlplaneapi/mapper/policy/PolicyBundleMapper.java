package io.securinest.controlplaneapi.mapper.policy;

import io.securinest.controlplaneapi.dto.policy.PolicyBundleResponse;
import io.securinest.controlplaneapi.dto.policy.SdkBundleResponse;
import io.securinest.controlplaneapi.entity.policy.PolicyBundle;
import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(
        componentModel = "spring",
        injectionStrategy = InjectionStrategy.CONSTRUCTOR,
        unmappedTargetPolicy = ReportingPolicy.IGNORE
)
public interface PolicyBundleMapper {

    @Mapping(target = "compiledJson", expression = "java(entity.getCompiledJson().toString())")
    @Mapping(target = "enforcementMode", expression = "java(entity.getEnforcementMode().name())")
    PolicyBundleResponse toResponse(PolicyBundle entity);

    @Mapping(target = "compiled", source = "compiledJson")
    @Mapping(target = "enforcementMode", expression = "java(entity.getEnforcementMode().name())")
    SdkBundleResponse toSdkResponse(PolicyBundle entity);

}
