package io.securinest.controlplaneapi.mapper.policy;

import io.securinest.controlplaneapi.dto.policy.SigningKeyResponse;
import io.securinest.controlplaneapi.entity.policy.SigningKey;
import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(
        componentModel = "spring",
        injectionStrategy = InjectionStrategy.CONSTRUCTOR,
        unmappedTargetPolicy = ReportingPolicy.IGNORE
)
public interface SigningKeyMapper {

    SigningKeyResponse toResponse(SigningKey entity);

}
